# E5 队列/事件与一致性（Streams/Kafka vs Outbox）

目标：验证任务分发与事件回放能力，避免后期一致性陷阱。

步骤：
1. A：编写 `scripts/streams_demo.py` 使用 Redis Streams（消费组、重放）。
2. B：Dramatiq/Celery + on-commit 入队示例 `scripts/task_demo.py`。
3. C：Outbox 原型 `scripts/outbox_demo.py`（事务ID高水位线读取 + 监控），仅作对比。
4. 进行故障注入并记录丢失率、重试成功率、端到端延迟，填入 `results/metrics.csv`。

## 接入真实技术栈（最小实施步骤）

1) Streams 实现（Redis/Kafka 任选其一）
- Redis Streams：创建消费组（`XGROUP CREATE`），生产/消费脚本演示重放、ACK；
- Kafka：创建主题与消费组，开启 `enable.auto.commit=false`，手动位点提交。

2) Outbox 实现（Postgres + 转发器）
- 事务写入业务表与 `outbox_events` 表；
- 独立转发器按事务ID/时间游标读取，转发到 Streams/Kafka；
- 记录成功/失败与重试，维持“最终至少一次”语义。

3) 指标与对比
- 统计成功率、重复率、乱序率、平均/分位延迟（端到端）；
- 保持输出 `results/metrics.csv` 与当前字段一致，便于与模拟对比。
