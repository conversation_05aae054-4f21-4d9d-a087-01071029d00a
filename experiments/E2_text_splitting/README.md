# E2 文本分割策略 Bakeoff

对比语义切分与朴素切分对检索质量与上下文贴合度的影响。

步骤：
1. 准备 `data/corpus/` 文档，与 E1 共用数据。
2. 运行 `scripts/split_semantic.py` 与 `scripts/split_naive.py` 生成切片。
3. 调用 E1 的评估脚本，比较不同切分策略的 Recall@K/nDCG。
4. 人工抽样检查跨标题污染率与 Chunk 统计。

## 接入真实技术栈（最小实施步骤）

目标：将切分策略与 E1 的真实检索评测打通，衡量对 Recall@K/nDCG 的实效影响。

1) 统一数据与切分输出
- 准备 `data/corpus/`，生成两套切分产物：
  - 朴素：`data/slices/naive/*.jsonl`
  - 语义：`data/slices/semantic/*.jsonl`
- 统一字段：`id, doc_id, chunk_id, content, start, end`

2) 嵌入与入库
- 使用与 E1 相同的 embedding 模型，批量编码两套切片；
- 将向量和元数据写入对应索引/集合（如 Postgres+pgvector 的 `docs_naive`、`docs_semantic`）。

3) 评测联动
- 复用 E1 的查询集与评测脚本，各自对 `naive` 与 `semantic` 库进行检索；
- 输出两份 `results/metrics_{naive,semantic}.csv`，关键指标对比：Recall@K、nDCG、命中片平均覆盖率等。

4) 汇总与结论
- 将对比结果并入根目录汇总，结合 `semantic_coherence_score` 作为辅佐参考；
- 以“检索效果提升/减速比”呈现收益-成本权衡。
