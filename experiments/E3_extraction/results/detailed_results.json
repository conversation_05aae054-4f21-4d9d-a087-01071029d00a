[{"method": "langextract", "processing_time_ms": 201.7667293548584, "success": true, "confidence_score": 0.85, "extracted_data": {"topic": "机器学习", "key_points": ["机器学习算法在数据分析中发挥重要作用", "深度学习模型能够自动提取特征", "神经网络的训练需要大量数据"], "entities": [], "summary": "机器学习算法在数据分析中发挥重要作用。深度学习模型能够自动提取特征。", "grounding": [{"text_span": "机器学习算法在数据分析中发挥重要作用", "start_char": 0, "end_char": 18, "confidence": 0.9}, {"text_span": "深度学习模型能够自动提取特征", "start_char": 19, "end_char": 33, "confidence": 0.9}, {"text_span": "神经网络的训练需要大量数据", "start_char": 34, "end_char": 47, "confidence": 0.9}]}, "error_message": null}, {"method": "rule_based", "processing_time_ms": 0.1552104949951172, "success": true, "confidence_score": 0.75, "extracted_data": {"topic": "机器学习", "key_points": [], "entities": [], "summary": "机器学习算法在数据分析中发挥重要作用。深度学习模型能够自动提取特征。"}, "error_message": null}, {"method": "langextract", "processing_time_ms": 202.5473117828369, "success": true, "confidence_score": 0.85, "extracted_data": {"topic": "数据库", "key_points": ["数据库系统的性能优化是关键问题", "索引可以显著提高查询速度", "事务处理确保数据一致性"], "entities": [], "summary": "数据库系统的性能优化是关键问题。索引可以显著提高查询速度。", "grounding": [{"text_span": "数据库系统的性能优化是关键问题", "start_char": 0, "end_char": 15, "confidence": 0.9}, {"text_span": "索引可以显著提高查询速度", "start_char": 16, "end_char": 28, "confidence": 0.9}, {"text_span": "事务处理确保数据一致性", "start_char": 29, "end_char": 40, "confidence": 0.9}]}, "error_message": null}, {"method": "rule_based", "processing_time_ms": 0.031948089599609375, "success": true, "confidence_score": 0.75, "extracted_data": {"topic": "数据库", "key_points": [], "entities": [], "summary": "数据库系统的性能优化是关键问题。索引可以显著提高查询速度。"}, "error_message": null}, {"method": "langextract", "processing_time_ms": 200.44517517089844, "success": true, "confidence_score": 0.85, "extracted_data": {"topic": "网络安全", "key_points": ["网络安全防护需要多层策略"], "entities": [], "summary": "网络安全防护需要多层策略。加密技术保护数据传输。", "grounding": [{"text_span": "网络安全防护需要多层策略", "start_char": 0, "end_char": 12, "confidence": 0.9}]}, "error_message": null}, {"method": "rule_based", "processing_time_ms": 0.025033950805664062, "success": true, "confidence_score": 0.75, "extracted_data": {"topic": "网络安全", "key_points": [], "entities": [], "summary": "网络安全防护需要多层策略。加密技术保护数据传输。"}, "error_message": null}, {"method": "langextract", "processing_time_ms": 201.55811309814453, "success": true, "confidence_score": 0.85, "extracted_data": {"topic": "软件工程", "key_points": ["软件开发流程包括需求分析、设计、编码、测试和部署"], "entities": [], "summary": "软件开发流程包括需求分析、设计、编码、测试和部署。敏捷开发方法提高效率。", "grounding": [{"text_span": "软件开发流程包括需求分析、设计、编码、测试和部署", "start_char": 0, "end_char": 24, "confidence": 0.9}]}, "error_message": null}, {"method": "rule_based", "processing_time_ms": 0.024080276489257812, "success": true, "confidence_score": 0.75, "extracted_data": {"topic": "软件工程", "key_points": ["软件开发流程包括需求分析、设计、编码、测试和部署"], "entities": [], "summary": "软件开发流程包括需求分析、设计、编码、测试和部署。敏捷开发方法提高效率。"}, "error_message": null}, {"method": "langextract", "processing_time_ms": 200.87122917175293, "success": true, "confidence_score": 0.85, "extracted_data": {"topic": "通用技术", "key_points": ["这是一段包含技术术语的复杂文本", "API接口设计需要考虑RESTful原则", "JSON格式广泛用于数据交换"], "entities": [{"text": "Tful", "type": "TECH_TERM", "confidence": 0.8}, {"text": "Web", "type": "TECH_TERM", "confidence": 0.8}], "summary": "这是一段包含技术术语的复杂文本。API接口设计需要考虑RESTful原则。", "grounding": [{"text_span": "这是一段包含技术术语的复杂文本", "start_char": 0, "end_char": 15, "confidence": 0.9}, {"text_span": "API接口设计需要考虑RESTful原则", "start_char": 16, "end_char": 36, "confidence": 0.9}, {"text_span": "JSON格式广泛用于数据交换", "start_char": 37, "end_char": 51, "confidence": 0.9}]}, "error_message": null}, {"method": "rule_based", "processing_time_ms": 0.01811981201171875, "success": true, "confidence_score": 0.75, "extracted_data": {"topic": "通用技术", "key_points": ["API接口设计需要考虑RESTful原则"], "entities": [], "summary": "这是一段包含技术术语的复杂文本。API接口设计需要考虑RESTful原则。"}, "error_message": null}]