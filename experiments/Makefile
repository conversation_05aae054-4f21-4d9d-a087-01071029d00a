# 实验框架 Makefile
# 用于运行各种技术对比实验

.PHONY: all setup clean e1 e2 e3 e4 e5 install-deps results summary

# 默认目标：运行所有实验
all: setup e1 e2 e3 e4 results

# 安装依赖
install-deps:
	@echo "安装实验依赖..."
	pip install -r requirements.txt
	@echo "依赖安装完成"

# 设置实验环境
setup:
	@echo "设置实验环境..."
	@mkdir -p E1_retrieval_engine/results
	@mkdir -p E2_text_splitting/results  
	@mkdir -p E3_extraction/results
	@mkdir -p E4_orchestration/results
	@mkdir -p E5_events_consistency/results
	@echo "实验目录创建完成"

# E1: 检索引擎对比实验 (pgvector vs Manticore)
e1:
	@echo "运行 E1: 检索引擎对比实验..."
	cd E1_retrieval_engine && python run_experiment.py
	@echo "E1 实验完成"

# E2: 文本分割对比实验 (语义 vs 朴素)
e2:
	@echo "运行 E2: 文本分割对比实验..."
	cd E2_text_splitting && python run_experiment.py
	@echo "E2 实验完成"

# E3: 结构化抽取对比实验 (langextract vs 规则化)
e3:
	@echo "运行 E3: 结构化抽取对比实验..."
	cd E3_extraction && python run_experiment.py
	@echo "E3 实验完成"

# E4: 编排对比实验 (Local vs LangGraph)
e4:
	@echo "运行 E4: 编排对比实验..."
	cd E4_orchestration && python run_experiment.py
	@echo "E4 实验完成"

# E5: 事件一致性对比实验 (Streams vs Outbox)
e5:
	@echo "运行 E5: 事件一致性对比实验..."
	@echo "E5 实验脚本待实现"
	# cd E5_events_consistency && python run_experiment.py

# 生成汇总结果
results:
	@echo "生成实验结果汇总..."
	python generate_summary.py
	@echo "结果汇总完成"

# 显示实验摘要
summary:
	@echo "实验结果摘要:"
	@echo "===================="
	@if [ -f E1_retrieval_engine/results/experiment_results.csv ]; then \
		echo "E1 检索引擎实验: ✓ 完成"; \
		echo "  结果文件: E1_retrieval_engine/results/experiment_results.csv"; \
	else \
		echo "E1 检索引擎实验: ✗ 未完成"; \
	fi
	@if [ -f E2_text_splitting/results/metrics.csv ]; then \
		echo "E2 文本分割实验: ✓ 完成"; \
		echo "  结果文件: E2_text_splitting/results/metrics.csv"; \
	else \
		echo "E2 文本分割实验: ✗ 未完成"; \
	fi
	@if [ -f E3_extraction/results/experiment_results.csv ]; then \
		echo "E3 结构化抽取实验: ✓ 完成"; \
		echo "  结果文件: E3_extraction/results/experiment_results.csv"; \
	else \
		echo "E3 结构化抽取实验: ✗ 未完成"; \
	fi
	@if [ -f E4_orchestration/results/orchestration_metrics.csv ]; then \
		echo "E4 编排对比实验: ✓ 完成"; \
		echo "  结果文件: E4_orchestration/results/orchestration_metrics.csv"; \
	else \
		echo "E4 编排对比实验: ✗ 未完成"; \
	fi
	@echo "===================="

# 清理实验结果
clean:
	@echo "清理实验结果..."
	rm -rf */results/*
	@echo "清理完成"

# 快速测试 (只运行核心实验)
quick: setup e1 e3
	@echo "快速测试完成"

# 完整测试 (运行所有实验)
full: setup e1 e2 e3 e4 results
	@echo "完整测试完成"

# 帮助信息
help:
	@echo "可用的 make 目标:"
	@echo "  all          - 运行所有实验"
	@echo "  setup        - 设置实验环境"
	@echo "  install-deps - 安装Python依赖"
	@echo "  e1           - 运行检索引擎对比实验"
	@echo "  e2           - 运行文本分割对比实验"
	@echo "  e3           - 运行结构化抽取对比实验"
	@echo "  e4           - 运行编排对比实验"
	@echo "  e5           - 运行事件一致性对比实验"
	@echo "  results      - 生成汇总结果"
	@echo "  summary      - 显示实验摘要"
	@echo "  quick        - 快速测试 (E1+E3)"
	@echo "  clean        - 清理实验结果"
	@echo "  help         - 显示此帮助信息"
