# Minimal services for real-stack experiments (E1/E5)
# - Postgres + pgvector for vector search (E1)
# - Redis for Streams demos (E5)
# - Manticore Search is optional (uncomment to compare)
#
# Usage:
#   cd experiments
#   docker compose up -d
#   docker compose ps
#   # connect: psql postgres://pguser:pgpass@localhost:5432/pgdb

version: "3.9"

services:
  postgres:
    image: pgvector/pgvector:pg16
    container_name: exp_pgvector
    environment:
      POSTGRES_DB: pgdb
      POSTGRES_USER: pguser
      POSTGRES_PASSWORD: pgpass
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $$POSTGRES_USER -d $$POSTGRES_DB"]
      interval: 5s
      timeout: 3s
      retries: 10
    volumes:
      - pg_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7
    container_name: exp_redis
    ports:
      - "6379:6379"
    command: ["redis-server", "--appendonly", "yes"]
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 10
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # manticore:
  #   image: manticoresearch/manticore:latest
  #   container_name: exp_manticore
  #   ports:
  #     - "9306:9306"   # SQL
  #     - "9308:9308"   # HTTP API
  #   healthcheck:
  #     test: ["CMD", "curl", "-sf", "http://localhost:9308/cli/json"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 10
  #   volumes:
  #     - manticore_data:/var/lib/manticore
  #   restart: unless-stopped

volumes:
  pg_data:
  redis_data:
  # manticore_data:

