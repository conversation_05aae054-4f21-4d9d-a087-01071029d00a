{"overall_summary": {"timestamp": "2025-08-21T22:45:40.940866", "experiments_completed": ["E1_retrieval_engine", "E3_extraction"], "recommendations": ["Manticore 检索速度比 pgvector 快 96.9%", "两种检索引擎的准确性相当", "规则化方法比 langextract 快 100.0%", "langextract 的置信度评估更准确"]}, "e1_results": {"pgvector_avg_time": 241.85276826222739, "manticore_avg_time": 7.558886210123698, "pgvector_avg_precision": 0.1, "manticore_avg_precision": 0.1, "total_queries": 10, "total_tests": 60, "pgvector_index_time_ms": 5022.4058628082275, "manticore_index_time_ms": 1040.9069061279297}, "e3_results": {"langextract_success_rate": 1.0, "rule_based_success_rate": 1.0, "langextract_avg_time": 201.43771171569824, "rule_based_avg_time": 0.050878524780273396, "langextract_avg_confidence": 0.85, "rule_based_avg_confidence": 0.75, "total_texts": 5, "total_extractions": 10}, "e2_results": {}, "e4_results": {}, "e5_results": {}}